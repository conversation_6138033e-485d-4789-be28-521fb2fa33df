{"name": "squad-wallet-contracts", "version": "1.0.0", "description": "Smart contracts for SquadWallet", "scripts": {"build": "hardhat compile", "test": "hardhat test", "deploy": "hardhat run scripts/deploy.ts --network base", "deploy:local": "hardhat run scripts/deploy.ts --network localhost", "verify": "hardhat run scripts/verify.ts --network base", "node": "hardhat node", "clean": "hardhat clean", "typechain": "hardhat typechain", "coverage": "hardhat coverage"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.2", "@nomicfoundation/hardhat-ethers": "^3.0.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.9", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.1", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.11", "@types/mocha": "^10.0.6", "chai": "^4.3.10", "ethers": "^6.8.1", "hardhat": "^2.19.2", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.5", "ts-node": "^10.9.1", "typechain": "^8.3.2", "typescript": "^5.3.3"}, "dependencies": {"@chainlink/contracts": "^0.8.0", "@openzeppelin/contracts": "^5.0.1"}}