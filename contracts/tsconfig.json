{"compilerOptions": {"target": "es2020", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "typeRoots": ["./node_modules/@types", "./typechain-types"]}, "include": ["./scripts", "./test", "./typechain-types/**/*"], "files": ["./hardhat.config.ts"]}