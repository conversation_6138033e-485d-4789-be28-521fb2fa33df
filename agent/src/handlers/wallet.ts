import { Command, CommandHandler } from '../types';
import { BlockchainService } from '../services/blockchain';
import { XMTPService } from '../services/xmtp';
import { parseEthAmount, validateCommand, formatEthAmount } from '../utils/parser';
import logger from '../utils/logger';

/**
 * Wallet-related command handlers
 */
export class WalletHandlers {
  constructor(
    private blockchainService: BlockchainService,
    private xmtpService: XMTPService
  ) {}

  /**
   * Create wallet command handler
   */
  createWallet: CommandHandler = {
    name: 'create-wallet',
    description: 'Create a new SquadWallet',
    usage: '/create-wallet <name>',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, [1, 10]);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.createWallet.usage}`;
        }

        const walletName = command.args.join(' ').trim();
        if (!walletName) {
          return '❌ Wallet name cannot be empty';
        }

        if (walletName.length > 50) {
          return '❌ Wallet name must be 50 characters or less';
        }

        logger.info('Creating wallet', {
          name: walletName,
          creator: command.sender
        });

        // For now, create wallet with just the sender as initial member
        // In a real implementation, you might parse additional members from the command
        const result = await this.blockchainService.createSquadWallet(
          walletName,
          [command.sender],
          ['Creator']
        );

        const response = `🎉 **SquadWallet Created Successfully!**

📝 **Name**: ${walletName}
📍 **Address**: \`${result.address}\`
🔗 **Transaction**: \`${result.txHash}\`
👥 **Members**: 1 (You)

Your new SquadWallet is ready! You can now:
• Deposit funds with \`/deposit <amount>\`
• Invite members to join
• Start playing games with \`/play dice <wager>\`

💡 Share the wallet address with your squad members!`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to create wallet', { error, command });
        const errorMessage = '❌ Failed to create wallet. Please try again later.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Deposit command handler
   */
  deposit: CommandHandler = {
    name: 'deposit',
    description: 'Deposit ETH to a SquadWallet',
    usage: '/deposit <amount> [wallet-address]',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, [1, 2]);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.deposit.usage}`;
        }

        const amountStr = command.args[0];
        let walletAddress = command.args[1];

        // Parse amount
        let amount: string;
        try {
          amount = parseEthAmount(amountStr);
        } catch (error) {
          return `❌ Invalid amount format. Use format like "0.1" or "0.1 ETH"`;
        }

        // If no wallet address provided, get user's first wallet
        if (!walletAddress) {
          const userWallets = await this.blockchainService.getUserWallets(command.sender);
          if (userWallets.length === 0) {
            return '❌ You don\'t have any wallets. Create one first with `/create-wallet <name>`';
          }
          walletAddress = userWallets[0];
        }

        logger.info('Processing deposit', {
          amount: formatEthAmount(amount),
          walletAddress,
          sender: command.sender
        });

        const txHash = await this.blockchainService.depositToWallet(walletAddress, amount);

        const response = `💰 **Deposit Successful!**

💵 **Amount**: ${formatEthAmount(amount)}
🏦 **Wallet**: \`${walletAddress}\`
🔗 **Transaction**: \`${txHash}\`

Your deposit has been confirmed! 🎉
• XP earned for this deposit
• Funds are now available for games and proposals`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to process deposit', { error, command });
        const errorMessage = '❌ Failed to process deposit. Please check your wallet balance and try again.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Balance command handler
   */
  balance: CommandHandler = {
    name: 'balance',
    description: 'Check wallet balance',
    usage: '/balance [wallet-address]',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, [0, 1]);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.balance.usage}`;
        }

        let walletAddress = command.args[0];

        // If no wallet address provided, get user's wallets
        if (!walletAddress) {
          const userWallets = await this.blockchainService.getUserWallets(command.sender);
          if (userWallets.length === 0) {
            return '❌ You don\'t have any wallets. Create one first with `/create-wallet <name>`';
          }

          // Show balances for all user wallets
          let response = '💰 **Your Wallet Balances**\n\n';
          
          for (const wallet of userWallets) {
            try {
              const walletInfo = await this.blockchainService.getWalletInfo(wallet);
              response += `🏦 **${walletInfo.name}**\n`;
              response += `   📍 \`${wallet}\`\n`;
              response += `   💵 ${walletInfo.balance} ETH\n`;
              response += `   👥 ${walletInfo.totalMembers} members\n\n`;
            } catch (error) {
              response += `🏦 **Wallet** \`${wallet}\`\n`;
              response += `   ❌ Error loading info\n\n`;
            }
          }

          await this.xmtpService.sendResponse(command.conversationId, response, true);
          return response;
        } else {
          // Show balance for specific wallet
          const walletInfo = await this.blockchainService.getWalletInfo(walletAddress);
          
          const response = `💰 **Wallet Balance**

🏦 **${walletInfo.name}**
📍 **Address**: \`${walletAddress}\`
💵 **Balance**: ${walletInfo.balance} ETH
👥 **Members**: ${walletInfo.totalMembers}
📅 **Created**: ${new Date(walletInfo.createdAt * 1000).toLocaleDateString()}`;

          await this.xmtpService.sendResponse(command.conversationId, response, true);
          return response;
        }
      } catch (error) {
        logger.error('Failed to get balance', { error, command });
        const errorMessage = '❌ Failed to get wallet balance. Please check the wallet address.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Wallets command handler
   */
  wallets: CommandHandler = {
    name: 'wallets',
    description: 'List your SquadWallets',
    usage: '/wallets',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 0);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.wallets.usage}`;
        }

        const userWallets = await this.blockchainService.getUserWallets(command.sender);
        
        if (userWallets.length === 0) {
          const response = `📭 **No Wallets Found**

You don't have any SquadWallets yet.

🚀 **Get Started:**
• Create your first wallet with \`/create-wallet <name>\`
• Join existing wallets by getting invited by other members

💡 **Tip**: SquadWallets are perfect for group expenses, gaming, and collaborative DeFi!`;

          await this.xmtpService.sendResponse(command.conversationId, response, true);
          return response;
        }

        let response = `🏦 **Your SquadWallets** (${userWallets.length})\n\n`;

        for (let i = 0; i < userWallets.length; i++) {
          try {
            const walletInfo = await this.blockchainService.getWalletInfo(userWallets[i]);
            response += `${i + 1}. **${walletInfo.name}**\n`;
            response += `   📍 \`${userWallets[i]}\`\n`;
            response += `   💵 ${walletInfo.balance} ETH\n`;
            response += `   👥 ${walletInfo.totalMembers} members\n`;
            response += `   📅 Created ${new Date(walletInfo.createdAt * 1000).toLocaleDateString()}\n\n`;
          } catch (error) {
            response += `${i + 1}. **Wallet** \`${userWallets[i]}\`\n`;
            response += `   ❌ Error loading details\n\n`;
          }
        }

        response += `💡 **Quick Actions:**
• Check balance: \`/balance\`
• Deposit funds: \`/deposit <amount>\`
• Start a game: \`/play dice <wager>\``;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to list wallets', { error, command });
        const errorMessage = '❌ Failed to list wallets. Please try again later.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Split command handler (show contribution breakdown)
   */
  split: CommandHandler = {
    name: 'split',
    description: 'Show wallet contribution breakdown',
    usage: '/split [wallet-address]',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, [0, 1]);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.split.usage}`;
        }

        let walletAddress = command.args[0];

        // If no wallet address provided, use user's first wallet
        if (!walletAddress) {
          const userWallets = await this.blockchainService.getUserWallets(command.sender);
          if (userWallets.length === 0) {
            return '❌ You don\'t have any wallets. Create one first with `/create-wallet <name>`';
          }
          walletAddress = userWallets[0];
        }

        const walletInfo = await this.blockchainService.getWalletInfo(walletAddress);
        
        // This is a simplified implementation
        // In a real implementation, you'd track individual member contributions
        const response = `📊 **Wallet Split Analysis**

🏦 **${walletInfo.name}**
📍 \`${walletAddress}\`
💰 **Total Balance**: ${walletInfo.balance} ETH
👥 **Members**: ${walletInfo.totalMembers}

📈 **Contribution Breakdown**:
${walletInfo.members.map((member, index) => 
  `${index + 1}. \`${member}\`: Equal share (${(parseFloat(walletInfo.balance) / walletInfo.totalMembers).toFixed(4)} ETH)`
).join('\n')}

💡 **Note**: This shows equal distribution. Individual contribution tracking is coming soon!`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to show split', { error, command });
        const errorMessage = '❌ Failed to show wallet split. Please check the wallet address.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Get all wallet handlers
   */
  getAllHandlers(): CommandHandler[] {
    return [
      this.createWallet,
      this.deposit,
      this.balance,
      this.wallets,
      this.split
    ];
  }
}
