import { Command, CommandHandler } from '../types';
import { BlockchainService } from '../services/blockchain';
import { XMTPService } from '../services/xmtp';
import { parseEthAmount, parseGameType, validateCommand, formatEthAmount } from '../utils/parser';
import logger from '../utils/logger';

/**
 * Game-related command handlers
 */
export class GameHandlers {
  constructor(
    private blockchainService: BlockchainService,
    private xmtpService: XMTPService
  ) {}

  /**
   * Play command handler (dice or coin flip)
   */
  play: CommandHandler = {
    name: 'play',
    description: 'Start a dice or coin flip game',
    usage: '/play <dice|coin> <wager>',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 2);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.play.usage}`;
        }

        const gameTypeStr = command.args[0];
        const wagerStr = command.args[1];

        // Parse game type
        let gameType: 'dice' | 'coin';
        try {
          gameType = parseGameType(gameTypeStr);
        } catch (error) {
          return `❌ Invalid game type. Use "dice" or "coin"`;
        }

        // Parse wager amount
        let wager: string;
        try {
          wager = parseEthAmount(wagerStr);
        } catch (error) {
          return `❌ Invalid wager amount. Use format like "0.01" or "0.01 ETH"`;
        }

        // Check minimum wager (0.001 ETH)
        const minWager = BigInt('1000000000000000'); // 0.001 ETH in wei
        if (BigInt(wager) < minWager) {
          return `❌ Minimum wager is 0.001 ETH`;
        }

        logger.info('Creating game', {
          gameType,
          wager: formatEthAmount(wager),
          creator: command.sender
        });

        let result;
        if (gameType === 'dice') {
          result = await this.blockchainService.createDiceGame(wager);
        } else {
          result = await this.blockchainService.createCoinFlipGame(wager);
        }

        const gameEmoji = gameType === 'dice' ? '🎲' : '🪙';
        const gameDescription = gameType === 'dice' 
          ? 'Dice Roll (highest roll wins)'
          : 'Coin Flip (50/50 chance)';

        const response = `${gameEmoji} **Game Created!**

🎮 **Type**: ${gameDescription}
💰 **Wager**: ${formatEthAmount(wager)}
🆔 **Game ID**: ${result.gameId}
🔗 **Transaction**: \`${result.txHash}\`

${gameType === 'dice' 
  ? `🎯 **How to Play**:
• Other players can join with \`/join ${result.gameId}\`
• Up to 6 players can participate
• Highest dice roll wins the pot!
• Game starts when you run \`/start ${result.gameId}\` or when full`
  : `🎯 **How to Play**:
• One more player can join with \`/join ${result.gameId}\`
• Game starts automatically when 2nd player joins
• 50/50 chance to win the pot!`
}

💡 **Share the Game ID with friends to let them join!**`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to create game', { error, command });
        const errorMessage = '❌ Failed to create game. Please check your balance and try again.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Join game command handler
   */
  join: CommandHandler = {
    name: 'join',
    description: 'Join an existing game',
    usage: '/join <gameId>',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 1);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.join.usage}`;
        }

        const gameIdStr = command.args[0];
        const gameId = parseInt(gameIdStr);

        if (isNaN(gameId) || gameId < 0) {
          return `❌ Invalid game ID. Must be a positive number.`;
        }

        // Get game info first
        const gameInfo = await this.blockchainService.getGameInfo(gameId);
        
        if (gameInfo.state !== 'pending') {
          return `❌ Game ${gameId} is not available for joining (status: ${gameInfo.state})`;
        }

        if (gameInfo.players.includes(command.sender)) {
          return `❌ You're already in this game!`;
        }

        const wager = parseEthAmount(gameInfo.wager);

        logger.info('Joining game', {
          gameId,
          wager: gameInfo.wager,
          player: command.sender
        });

        const txHash = await this.blockchainService.joinGame(gameId, wager);

        const gameEmoji = gameInfo.type === 'dice' ? '🎲' : '🪙';
        const response = `${gameEmoji} **Joined Game Successfully!**

🆔 **Game ID**: ${gameId}
🎮 **Type**: ${gameInfo.type === 'dice' ? 'Dice Roll' : 'Coin Flip'}
💰 **Wager**: ${gameInfo.wager} ETH
👥 **Players**: ${gameInfo.players.length + 1}
🔗 **Transaction**: \`${txHash}\`

${gameInfo.type === 'dice' 
  ? `🎯 **Next Steps**:
• Wait for more players or game creator to start
• Game needs at least 2 players to begin
• May the highest roll win! 🎲`
  : `🎯 **Game Starting**:
• Coin flip game starts automatically with 2 players
• 50/50 chance to win the pot!
• Good luck! 🪙`
}`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to join game', { error, command });
        const errorMessage = '❌ Failed to join game. Please check the game ID and your balance.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Games command handler (list active games)
   */
  games: CommandHandler = {
    name: 'games',
    description: 'List active games',
    usage: '/games',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 0);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.games.usage}`;
        }

        // This is a simplified implementation
        // In a real implementation, you'd query recent games from the contract
        const response = `🎮 **Active Games**

🎲 **Dice Games**:
• Game #123 - 0.01 ETH - 2/6 players - \`/join 123\`
• Game #124 - 0.05 ETH - 1/6 players - \`/join 124\`

🪙 **Coin Flip Games**:
• Game #125 - 0.02 ETH - 1/2 players - \`/join 125\`

🚀 **Start Your Own**:
• \`/play dice 0.01\` - Start a dice game
• \`/play coin 0.01\` - Start a coin flip game

💡 **Game Rules**:
• **Dice**: Up to 6 players, highest roll wins
• **Coin Flip**: 2 players, 50/50 chance
• **Minimum wager**: 0.001 ETH
• **House fee**: 2% of total pot

📊 **Your Stats**: Use \`/stats\` to see your gaming history`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to list games', { error, command });
        const errorMessage = '❌ Failed to list games. Please try again later.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Game info command handler
   */
  gameInfo: CommandHandler = {
    name: 'game',
    description: 'Get information about a specific game',
    usage: '/game <gameId>',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 1);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.gameInfo.usage}`;
        }

        const gameIdStr = command.args[0];
        const gameId = parseInt(gameIdStr);

        if (isNaN(gameId) || gameId < 0) {
          return `❌ Invalid game ID. Must be a positive number.`;
        }

        const gameInfo = await this.blockchainService.getGameInfo(gameId);
        const gameEmoji = gameInfo.type === 'dice' ? '🎲' : '🪙';
        
        let statusEmoji = '';
        switch (gameInfo.state) {
          case 'pending': statusEmoji = '⏳'; break;
          case 'active': statusEmoji = '🎮'; break;
          case 'completed': statusEmoji = '✅'; break;
          case 'cancelled': statusEmoji = '❌'; break;
        }

        const response = `${gameEmoji} **Game Information**

🆔 **Game ID**: ${gameId}
🎮 **Type**: ${gameInfo.type === 'dice' ? 'Dice Roll' : 'Coin Flip'}
${statusEmoji} **Status**: ${gameInfo.state.charAt(0).toUpperCase() + gameInfo.state.slice(1)}
💰 **Wager**: ${gameInfo.wager} ETH
👤 **Creator**: \`${gameInfo.creator}\`
👥 **Players** (${gameInfo.players.length}):
${gameInfo.players.map((player, index) => `   ${index + 1}. \`${player}\``).join('\n')}

${gameInfo.winner ? `🏆 **Winner**: \`${gameInfo.winner}\`` : ''}

${gameInfo.state === 'pending' 
  ? `🎯 **How to Join**: \`/join ${gameId}\``
  : gameInfo.state === 'active'
  ? `⏳ **Game in progress** - waiting for results...`
  : gameInfo.state === 'completed'
  ? `🎉 **Game completed!**`
  : `❌ **Game cancelled**`
}`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to get game info', { error, command });
        const errorMessage = '❌ Failed to get game information. Please check the game ID.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Start dice game command handler
   */
  start: CommandHandler = {
    name: 'start',
    description: 'Start a dice game (creator only)',
    usage: '/start <gameId>',
    handler: async (command: Command): Promise<string> => {
      try {
        const validation = validateCommand(command, 1);
        if (!validation.valid) {
          return `❌ ${validation.error}\nUsage: ${this.start.usage}`;
        }

        const gameIdStr = command.args[0];
        const gameId = parseInt(gameIdStr);

        if (isNaN(gameId) || gameId < 0) {
          return `❌ Invalid game ID. Must be a positive number.`;
        }

        // Get game info first
        const gameInfo = await this.blockchainService.getGameInfo(gameId);
        
        if (gameInfo.creator !== command.sender) {
          return `❌ Only the game creator can start the game.`;
        }

        if (gameInfo.state !== 'pending') {
          return `❌ Game ${gameId} cannot be started (status: ${gameInfo.state})`;
        }

        if (gameInfo.type !== 'dice') {
          return `❌ Only dice games can be manually started. Coin flip games start automatically.`;
        }

        if (gameInfo.players.length < 2) {
          return `❌ Need at least 2 players to start the game. Current players: ${gameInfo.players.length}`;
        }

        // Note: In a real implementation, you'd call the contract's startDiceGame function
        // For now, we'll just show a message
        const response = `🎲 **Starting Dice Game...**

🆔 **Game ID**: ${gameId}
👥 **Players**: ${gameInfo.players.length}
💰 **Total Pot**: ${(parseFloat(gameInfo.wager) * gameInfo.players.length).toFixed(4)} ETH

⏳ **Requesting randomness from Chainlink VRF...**
🎯 **Results will be available shortly!**

💡 **How it works**:
• Each player gets a random dice roll (1-6)
• Highest roll wins the entire pot
• Results are provably fair using Chainlink VRF`;

        await this.xmtpService.sendResponse(command.conversationId, response, true);
        return response;
      } catch (error) {
        logger.error('Failed to start game', { error, command });
        const errorMessage = '❌ Failed to start game. Please try again later.';
        await this.xmtpService.sendResponse(command.conversationId, errorMessage, false);
        return errorMessage;
      }
    }
  };

  /**
   * Get all game handlers
   */
  getAllHandlers(): CommandHandler[] {
    return [
      this.play,
      this.join,
      this.games,
      this.gameInfo,
      this.start
    ];
  }
}
