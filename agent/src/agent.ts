import * as dotenv from 'dotenv';
import { AgentConfig, Command, XMTPMessage, CommandHandler } from './types';
import { XMTPService } from './services/xmtp';
import { BlockchainService } from './services/blockchain';
import { PriceService } from './services/price';
import { WalletHandlers } from './handlers/wallet';
import { GameHandlers } from './handlers/game';
import { InfoHandlers } from './handlers/info';
import { parseCommand } from './utils/parser';
import logger from './utils/logger';
import * as cron from 'node-cron';

// Load environment variables
dotenv.config({ path: '../.env' });

/**
 * Main SquadWallet Agent class
 */
export class SquadWalletAgent {
  private xmtpService: XMTPService;
  private blockchainService: BlockchainService;
  private priceService: PriceService;
  private commandHandlers: Map<string, CommandHandler> = new Map();
  private config: AgentConfig;

  constructor() {
    // Initialize configuration
    this.config = this.loadConfig();
    
    // Initialize services
    this.xmtpService = new XMTPService(
      this.config.xmtpPrivateKey,
      this.config.xmtpEnv
    );
    
    this.blockchainService = new BlockchainService(this.config);
    this.priceService = new PriceService();

    // Initialize command handlers
    this.initializeHandlers();

    logger.info('SquadWallet Agent initialized', {
      agentAddress: this.blockchainService.getAgentAddress(),
      xmtpEnv: this.config.xmtpEnv
    });
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfig(): AgentConfig {
    const requiredEnvVars = [
      'XMTP_PRIVATE_KEY',
      'CDP_API_KEY_NAME',
      'CDP_API_KEY_PRIVATE_KEY',
      'BASE_RPC_URL',
      'SQUAD_WALLET_FACTORY',
      'GAME_MANAGER_CONTRACT',
      'XP_BADGES_CONTRACT'
    ];

    // Check for required environment variables
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`);
      }
    }

    return {
      xmtpPrivateKey: process.env.XMTP_PRIVATE_KEY!,
      xmtpEnv: (process.env.XMTP_ENV as 'dev' | 'production') || 'production',
      cdpApiKeyName: process.env.CDP_API_KEY_NAME!,
      cdpApiKeyPrivateKey: process.env.CDP_API_KEY_PRIVATE_KEY!,
      baseRpcUrl: process.env.BASE_RPC_URL!,
      contracts: {
        squadWalletFactory: process.env.SQUAD_WALLET_FACTORY!,
        gameManager: process.env.GAME_MANAGER_CONTRACT!,
        xpBadges: process.env.XP_BADGES_CONTRACT!
      }
    };
  }

  /**
   * Initialize command handlers
   */
  private initializeHandlers(): void {
    const walletHandlers = new WalletHandlers(this.blockchainService, this.xmtpService);
    const gameHandlers = new GameHandlers(this.blockchainService, this.xmtpService);
    const infoHandlers = new InfoHandlers(this.blockchainService, this.xmtpService, this.priceService);

    // Register all handlers
    const allHandlers = [
      ...walletHandlers.getAllHandlers(),
      ...gameHandlers.getAllHandlers(),
      ...infoHandlers.getAllHandlers()
    ];

    for (const handler of allHandlers) {
      this.commandHandlers.set(handler.name, handler);
    }

    logger.info('Command handlers initialized', {
      handlerCount: this.commandHandlers.size,
      handlers: Array.from(this.commandHandlers.keys())
    });
  }

  /**
   * Start the agent
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting SquadWallet Agent...');

      // Initialize blockchain service
      await this.blockchainService.initializeCoinbaseWallet();

      // Initialize XMTP service
      await this.xmtpService.initialize();

      // Register message handler
      this.xmtpService.registerMessageHandler('commandProcessor', this.handleMessage.bind(this));

      // Start scheduled tasks
      this.startScheduledTasks();

      logger.info('SquadWallet Agent started successfully! 🚀');
      
      // Send startup notification (optional)
      await this.sendStartupNotification();

    } catch (error) {
      logger.error('Failed to start agent', { error });
      throw error;
    }
  }

  /**
   * Handle incoming XMTP messages
   */
  private async handleMessage(message: XMTPMessage): Promise<void> {
    try {
      logger.info('Processing message', {
        from: message.senderAddress,
        conversationId: message.conversationId,
        contentPreview: message.content.substring(0, 50) + '...'
      });

      // Parse command from message
      const command = parseCommand(
        message.content,
        message.senderAddress,
        message.conversationId
      );

      if (!command) {
        // Not a command, ignore or send help
        if (message.content.toLowerCase().includes('help') || 
            message.content.toLowerCase().includes('squad')) {
          await this.xmtpService.sendHelpMessage(message.conversationId);
        }
        return;
      }

      // Find and execute command handler
      const handler = this.commandHandlers.get(command.name);
      if (!handler) {
        const response = `❌ Unknown command: \`/${command.name}\`\n\nUse \`/help\` to see available commands.`;
        await this.xmtpService.sendResponse(message.conversationId, response, false);
        return;
      }

      // Execute command
      logger.info('Executing command', {
        command: command.name,
        args: command.args,
        sender: command.sender
      });

      await handler.handler(command);

    } catch (error) {
      logger.error('Error handling message', { error, message });
      
      try {
        await this.xmtpService.sendResponse(
          message.conversationId,
          '❌ An error occurred while processing your request. Please try again later.',
          false
        );
      } catch (sendError) {
        logger.error('Failed to send error response', { sendError });
      }
    }
  }

  /**
   * Start scheduled tasks (price alerts, etc.)
   */
  private startScheduledTasks(): void {
    // Price update task - every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
      try {
        logger.debug('Running scheduled price update');
        // Clear price cache to ensure fresh data
        this.priceService.clearCache();
      } catch (error) {
        logger.error('Error in scheduled price update', { error });
      }
    });

    // Health check task - every hour
    cron.schedule('0 * * * *', async () => {
      try {
        logger.info('Running health check');
        const agentBalance = await this.blockchainService.getAgentBalance();
        logger.info('Agent health check', {
          balance: agentBalance,
          xmtpConnected: this.xmtpService.isInitialized()
        });
      } catch (error) {
        logger.error('Error in health check', { error });
      }
    });

    // Daily stats task - every day at midnight
    cron.schedule('0 0 * * *', async () => {
      try {
        logger.info('Running daily stats collection');
        // In a real implementation, you might collect and store daily statistics
      } catch (error) {
        logger.error('Error in daily stats collection', { error });
      }
    });

    logger.info('Scheduled tasks started');
  }

  /**
   * Send startup notification (optional)
   */
  private async sendStartupNotification(): Promise<void> {
    try {
      // In a real implementation, you might send a notification to admin channels
      logger.info('Agent startup complete', {
        timestamp: new Date().toISOString(),
        agentAddress: this.blockchainService.getAgentAddress()
      });
    } catch (error) {
      logger.error('Failed to send startup notification', { error });
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down SquadWallet Agent...');
      
      // Disconnect XMTP
      await this.xmtpService.disconnect();
      
      logger.info('SquadWallet Agent shutdown complete');
    } catch (error) {
      logger.error('Error during shutdown', { error });
    }
  }

  /**
   * Get agent status
   */
  getStatus(): {
    isRunning: boolean;
    agentAddress: string;
    xmtpConnected: boolean;
    commandHandlers: number;
  } {
    return {
      isRunning: true,
      agentAddress: this.blockchainService.getAgentAddress(),
      xmtpConnected: this.xmtpService.isInitialized(),
      commandHandlers: this.commandHandlers.size
    };
  }
}

/**
 * Main execution function
 */
async function main() {
  const agent = new SquadWalletAgent();

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await agent.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await agent.shutdown();
    process.exit(0);
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception', { error });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection', { reason, promise });
    process.exit(1);
  });

  try {
    await agent.start();
    
    // Keep the process running
    setInterval(() => {
      const status = agent.getStatus();
      logger.debug('Agent status check', status);
    }, 60000); // Check every minute

  } catch (error) {
    logger.error('Failed to start agent', { error });
    process.exit(1);
  }
}

// Run the agent if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    logger.error('Fatal error in main', { error });
    process.exit(1);
  });
}

export default SquadWalletAgent;
