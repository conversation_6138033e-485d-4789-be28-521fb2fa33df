import { Client } from '@xmtp/xmtp-js';
import { ethers } from 'ethers';
import { XMTPMessage } from '../types';
import logger from '../utils/logger';

/**
 * XMTP service for handling messaging
 */
export class XMTPService {
  private client: Client | null = null;
  private wallet: ethers.Wallet;
  private messageHandlers: Map<string, (message: XMTPMessage) => Promise<void>> = new Map();

  constructor(privateKey: string, env: 'dev' | 'production' = 'production') {
    this.wallet = new ethers.Wallet(privateKey);
    logger.info('XMTPService initialized', {
      address: this.wallet.address,
      env
    });
  }

  /**
   * Initialize XMTP client
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing XMTP client...');
      
      this.client = await Client.create(this.wallet, {
        env: process.env.XMTP_ENV === 'dev' ? 'dev' : 'production'
      });

      logger.info('XMTP client initialized successfully', {
        address: this.client.address
      });

      // Start listening for messages
      await this.startListening();
    } catch (error) {
      logger.error('Failed to initialize XMTP client', { error });
      throw error;
    }
  }

  /**
   * Start listening for new messages
   */
  private async startListening(): Promise<void> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      logger.info('Starting to listen for messages...');

      // Listen for new conversations
      for await (const conversation of await this.client.conversations.stream()) {
        logger.info('New conversation detected', {
          peerAddress: conversation.peerAddress,
          topic: conversation.topic
        });

        // Listen for messages in this conversation
        this.listenToConversation(conversation);
      }
    } catch (error) {
      logger.error('Error in message listening', { error });
    }
  }

  /**
   * Listen to messages in a specific conversation
   */
  private async listenToConversation(conversation: any): Promise<void> {
    try {
      for await (const message of await conversation.streamMessages()) {
        if (message.senderAddress === this.client?.address) {
          // Skip messages sent by the agent itself
          continue;
        }

        const xmtpMessage: XMTPMessage = {
          content: message.content,
          senderAddress: message.senderAddress,
          conversationId: conversation.topic,
          timestamp: message.sent
        };

        logger.info('Received message', {
          from: message.senderAddress,
          content: message.content.substring(0, 100) + '...',
          conversationId: conversation.topic
        });

        // Process the message
        await this.processMessage(xmtpMessage, conversation);
      }
    } catch (error) {
      logger.error('Error listening to conversation', { error });
    }
  }

  /**
   * Process incoming message
   */
  private async processMessage(message: XMTPMessage, conversation: any): Promise<void> {
    try {
      // Call all registered message handlers
      for (const [handlerName, handler] of this.messageHandlers) {
        try {
          await handler(message);
        } catch (error) {
          logger.error(`Error in message handler ${handlerName}`, { error });
        }
      }
    } catch (error) {
      logger.error('Error processing message', { error });
    }
  }

  /**
   * Send a message to a conversation
   */
  async sendMessage(conversationTopic: string, content: string): Promise<void> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      const conversations = await this.client.conversations.list();
      const conversation = conversations.find(c => c.topic === conversationTopic);

      if (!conversation) {
        throw new Error('Conversation not found');
      }

      await conversation.send(content);
      
      logger.info('Message sent successfully', {
        conversationTopic,
        contentLength: content.length
      });
    } catch (error) {
      logger.error('Failed to send message', { conversationTopic, error });
      throw error;
    }
  }

  /**
   * Send a message to a specific address
   */
  async sendMessageToAddress(address: string, content: string): Promise<void> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      // Check if we can message this address
      const canMessage = await this.client.canMessage(address);
      if (!canMessage) {
        throw new Error(`Cannot send message to ${address} - they may not have XMTP enabled`);
      }

      // Create or get existing conversation
      const conversation = await this.client.conversations.newConversation(address);
      await conversation.send(content);

      logger.info('Direct message sent successfully', {
        to: address,
        contentLength: content.length
      });
    } catch (error) {
      logger.error('Failed to send direct message', { address, error });
      throw error;
    }
  }

  /**
   * Register a message handler
   */
  registerMessageHandler(name: string, handler: (message: XMTPMessage) => Promise<void>): void {
    this.messageHandlers.set(name, handler);
    logger.info('Message handler registered', { name });
  }

  /**
   * Unregister a message handler
   */
  unregisterMessageHandler(name: string): void {
    this.messageHandlers.delete(name);
    logger.info('Message handler unregistered', { name });
  }

  /**
   * Get all conversations
   */
  async getConversations(): Promise<any[]> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      const conversations = await this.client.conversations.list();
      return conversations;
    } catch (error) {
      logger.error('Failed to get conversations', { error });
      throw error;
    }
  }

  /**
   * Get messages from a conversation
   */
  async getMessages(conversationTopic: string, limit: number = 50): Promise<any[]> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      const conversations = await this.client.conversations.list();
      const conversation = conversations.find(c => c.topic === conversationTopic);

      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const messages = await conversation.messages({ limit });
      return messages;
    } catch (error) {
      logger.error('Failed to get messages', { conversationTopic, error });
      throw error;
    }
  }

  /**
   * Check if an address can receive messages
   */
  async canMessage(address: string): Promise<boolean> {
    if (!this.client) {
      throw new Error('XMTP client not initialized');
    }

    try {
      return await this.client.canMessage(address);
    } catch (error) {
      logger.error('Failed to check if can message', { address, error });
      return false;
    }
  }

  /**
   * Get the agent's XMTP address
   */
  getAddress(): string {
    return this.wallet.address;
  }

  /**
   * Check if client is initialized
   */
  isInitialized(): boolean {
    return this.client !== null;
  }

  /**
   * Disconnect the client
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      // XMTP client doesn't have explicit disconnect method
      // Just clear the reference
      this.client = null;
      logger.info('XMTP client disconnected');
    }
  }

  /**
   * Send a formatted response message
   */
  async sendResponse(
    conversationTopic: string,
    message: string,
    success: boolean = true,
    data?: any
  ): Promise<void> {
    const emoji = success ? '✅' : '❌';
    const formattedMessage = `${emoji} ${message}`;
    
    if (data) {
      const dataString = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
      await this.sendMessage(conversationTopic, `${formattedMessage}\n\n${dataString}`);
    } else {
      await this.sendMessage(conversationTopic, formattedMessage);
    }
  }

  /**
   * Send a help message with available commands
   */
  async sendHelpMessage(conversationTopic: string): Promise<void> {
    const helpMessage = `
🤖 **SquadWallet Agent Commands**

**Wallet Management:**
• \`/create-wallet <name>\` - Create a new squad wallet
• \`/deposit <amount>\` - Deposit ETH to your wallet
• \`/balance\` - Check wallet balance
• \`/wallets\` - List your wallets

**Games:**
• \`/play dice <wager>\` - Start a dice game
• \`/play coin <wager>\` - Start a coin flip game
• \`/join <gameId>\` - Join an existing game
• \`/games\` - List active games

**Information:**
• \`/price <token>\` - Get token price
• \`/stats\` - View your statistics
• \`/xp\` - Check your XP and badges
• \`/leaderboard\` - View XP leaderboard

**Proposals:**
• \`/propose <description>\` - Create a proposal
• \`/vote <proposalId> <yes/no>\` - Vote on a proposal

**Help:**
• \`/help\` - Show this help message

💡 **Tips:**
- Use ETH amounts like "0.1" or "0.1 ETH"
- Games require minimum 0.001 ETH wager
- All transactions are on Base mainnet
    `;

    await this.sendMessage(conversationTopic, helpMessage.trim());
  }
}
