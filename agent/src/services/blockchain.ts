import { ethers } from 'ethers';
import { Coinbase, Wallet } from '@coinbase/coinbase-sdk';
import { AgentConfig, ContractAddresses, WalletInfo, GameInfo, UserStats } from '../types';
import logger from '../utils/logger';

/**
 * Blockchain service for interacting with SquadWallet contracts
 */
export class BlockchainService {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private coinbaseWallet: Wallet;
  private contracts: ContractAddresses;

  // Contract ABIs (simplified for key functions)
  private squadWalletFactoryABI = [
    'function createSquadWallet(string memory walletName, address[] memory initialMembers, string[] memory memberNames) external returns (address)',
    'function getUserWallets(address user) external view returns (address[] memory)',
    'function getWalletInfo(address walletAddress) external view returns (string memory name, uint256 totalMembers, uint256 createdAt, address[] memory members)',
    'function getUserGlobalStats(address user) external view returns (uint256 walletsCount, uint256 totalXP, uint256 gamesPlayed, uint256 gamesWon, uint256 totalDeposited, uint256 proposalsVoted)',
    'event SquadWalletCreated(address indexed walletAddress, address indexed creator, string walletName, address[] members)'
  ];

  private squadWalletABI = [
    'function depositETH() external payable',
    'function getBalance(address token) external view returns (uint256)',
    'function createProposal(string memory description, address target, uint256 value, bytes memory data) external returns (uint256)',
    'function vote(uint256 proposalId, bool support) external',
    'function getAllMembers() external view returns (address[] memory)',
    'function getMember(address member) external view returns (string memory name, uint256 totalDeposited, uint256 xpPoints, bool isActive, uint256 joinedAt)',
    'function walletName() external view returns (string memory)'
  ];

  private gameManagerABI = [
    'function createDiceGame(uint256 wager) external payable returns (uint256)',
    'function createCoinFlipGame(uint256 wager) external payable returns (uint256)',
    'function joinGame(uint256 gameId) external payable',
    'function getGame(uint256 gameId) external view returns (uint256 id, address creator, uint8 gameType, uint256 wager, uint256 totalPot, address[] memory players, uint8 state, uint256 createdAt, address winner)',
    'function getPlayerStats(address player) external view returns (uint256 gamesPlayed, uint256 gamesWon, uint256 totalWagered, uint256 totalWon, uint256 xpEarned)',
    'event GameCreated(uint256 indexed gameId, address indexed creator, uint8 gameType, uint256 wager)',
    'event GameCompleted(uint256 indexed gameId, address indexed winner, uint256 payout, uint256 randomResult)'
  ];

  private xpBadgesABI = [
    'function getUserXP(address user) external view returns (uint256)',
    'function getUserStats(address user) external view returns (uint256 totalXP, uint256 gamesPlayed, uint256 gamesWon, uint256 totalDeposited, uint256 proposalsVoted, uint256 walletsCreated, uint256 streakDays)',
    'function getUserBadgeLevel(address user, uint8 badgeType) external view returns (uint256)',
    'function balanceOf(address owner) external view returns (uint256)',
    'function tokenOfOwnerByIndex(address owner, uint256 index) external view returns (uint256)',
    'function tokenURI(uint256 tokenId) external view returns (string memory)'
  ];

  constructor(config: AgentConfig) {
    this.provider = new ethers.JsonRpcProvider(config.baseRpcUrl);
    this.contracts = config.contracts;
    
    // Initialize Coinbase SDK
    Coinbase.configure({
      apiKeyName: config.cdpApiKeyName,
      privateKey: config.cdpApiKeyPrivateKey,
    });

    // Create wallet from private key for direct transactions
    this.wallet = new ethers.Wallet(config.xmtpPrivateKey, this.provider);
    
    logger.info('BlockchainService initialized', {
      walletAddress: this.wallet.address,
      contracts: this.contracts
    });
  }

  /**
   * Initialize Coinbase wallet
   */
  async initializeCoinbaseWallet(): Promise<void> {
    try {
      this.coinbaseWallet = await Wallet.create();
      logger.info('Coinbase wallet created', { 
        walletId: this.coinbaseWallet.getId() 
      });
    } catch (error) {
      logger.error('Failed to create Coinbase wallet', { error });
      throw error;
    }
  }

  /**
   * Create a new SquadWallet
   */
  async createSquadWallet(
    walletName: string,
    memberAddresses: string[],
    memberNames: string[]
  ): Promise<{ address: string; txHash: string }> {
    try {
      const factory = new ethers.Contract(
        this.contracts.squadWalletFactory,
        this.squadWalletFactoryABI,
        this.wallet
      );

      logger.info('Creating SquadWallet', {
        name: walletName,
        members: memberAddresses.length
      });

      const tx = await factory.createSquadWallet(
        walletName,
        memberAddresses,
        memberNames
      );

      const receipt = await tx.wait();
      
      // Extract wallet address from event
      const walletCreatedEvent = receipt.logs.find(
        (log: any) => log.fragment?.name === 'SquadWalletCreated'
      );
      
      const walletAddress = walletCreatedEvent?.args?.[0];

      logger.info('SquadWallet created successfully', {
        walletAddress,
        txHash: receipt.hash
      });

      return {
        address: walletAddress,
        txHash: receipt.hash
      };
    } catch (error) {
      logger.error('Failed to create SquadWallet', { error });
      throw error;
    }
  }

  /**
   * Get wallet information
   */
  async getWalletInfo(walletAddress: string): Promise<WalletInfo> {
    try {
      const factory = new ethers.Contract(
        this.contracts.squadWalletFactory,
        this.squadWalletFactoryABI,
        this.provider
      );

      const wallet = new ethers.Contract(
        walletAddress,
        this.squadWalletABI,
        this.provider
      );

      const [name, totalMembers, createdAt, members] = await factory.getWalletInfo(walletAddress);
      const balance = await wallet.getBalance(ethers.ZeroAddress);

      return {
        address: walletAddress,
        name,
        members,
        totalMembers: Number(totalMembers),
        createdAt: Number(createdAt),
        balance: ethers.formatEther(balance)
      };
    } catch (error) {
      logger.error('Failed to get wallet info', { walletAddress, error });
      throw error;
    }
  }

  /**
   * Get user's wallets
   */
  async getUserWallets(userAddress: string): Promise<string[]> {
    try {
      const factory = new ethers.Contract(
        this.contracts.squadWalletFactory,
        this.squadWalletFactoryABI,
        this.provider
      );

      const wallets = await factory.getUserWallets(userAddress);
      return wallets;
    } catch (error) {
      logger.error('Failed to get user wallets', { userAddress, error });
      throw error;
    }
  }

  /**
   * Deposit ETH to a wallet
   */
  async depositToWallet(
    walletAddress: string,
    amount: string
  ): Promise<string> {
    try {
      const wallet = new ethers.Contract(
        walletAddress,
        this.squadWalletABI,
        this.wallet
      );

      logger.info('Depositing to wallet', {
        walletAddress,
        amount: ethers.formatEther(amount)
      });

      const tx = await wallet.depositETH({ value: amount });
      const receipt = await tx.wait();

      logger.info('Deposit successful', {
        txHash: receipt.hash,
        gasUsed: receipt.gasUsed.toString()
      });

      return receipt.hash;
    } catch (error) {
      logger.error('Failed to deposit to wallet', { walletAddress, amount, error });
      throw error;
    }
  }

  /**
   * Create a dice game
   */
  async createDiceGame(wager: string): Promise<{ gameId: number; txHash: string }> {
    try {
      const gameManager = new ethers.Contract(
        this.contracts.gameManager,
        this.gameManagerABI,
        this.wallet
      );

      logger.info('Creating dice game', {
        wager: ethers.formatEther(wager)
      });

      const tx = await gameManager.createDiceGame(wager, { value: wager });
      const receipt = await tx.wait();

      // Extract game ID from event
      const gameCreatedEvent = receipt.logs.find(
        (log: any) => log.fragment?.name === 'GameCreated'
      );
      
      const gameId = Number(gameCreatedEvent?.args?.[0] || 0);

      logger.info('Dice game created', {
        gameId,
        txHash: receipt.hash
      });

      return {
        gameId,
        txHash: receipt.hash
      };
    } catch (error) {
      logger.error('Failed to create dice game', { wager, error });
      throw error;
    }
  }

  /**
   * Create a coin flip game
   */
  async createCoinFlipGame(wager: string): Promise<{ gameId: number; txHash: string }> {
    try {
      const gameManager = new ethers.Contract(
        this.contracts.gameManager,
        this.gameManagerABI,
        this.wallet
      );

      logger.info('Creating coin flip game', {
        wager: ethers.formatEther(wager)
      });

      const tx = await gameManager.createCoinFlipGame(wager, { value: wager });
      const receipt = await tx.wait();

      // Extract game ID from event
      const gameCreatedEvent = receipt.logs.find(
        (log: any) => log.fragment?.name === 'GameCreated'
      );
      
      const gameId = Number(gameCreatedEvent?.args?.[0] || 0);

      logger.info('Coin flip game created', {
        gameId,
        txHash: receipt.hash
      });

      return {
        gameId,
        txHash: receipt.hash
      };
    } catch (error) {
      logger.error('Failed to create coin flip game', { wager, error });
      throw error;
    }
  }

  /**
   * Join a game
   */
  async joinGame(gameId: number, wager: string): Promise<string> {
    try {
      const gameManager = new ethers.Contract(
        this.contracts.gameManager,
        this.gameManagerABI,
        this.wallet
      );

      logger.info('Joining game', { gameId, wager: ethers.formatEther(wager) });

      const tx = await gameManager.joinGame(gameId, { value: wager });
      const receipt = await tx.wait();

      logger.info('Joined game successfully', {
        gameId,
        txHash: receipt.hash
      });

      return receipt.hash;
    } catch (error) {
      logger.error('Failed to join game', { gameId, wager, error });
      throw error;
    }
  }

  /**
   * Get game information
   */
  async getGameInfo(gameId: number): Promise<GameInfo> {
    try {
      const gameManager = new ethers.Contract(
        this.contracts.gameManager,
        this.gameManagerABI,
        this.provider
      );

      const [id, creator, gameType, wager, totalPot, players, state, createdAt, winner] = 
        await gameManager.getGame(gameId);

      const gameTypeMap = ['dice', 'coinflip'] as const;
      const stateMap = ['pending', 'active', 'completed', 'cancelled'] as const;

      return {
        id: Number(id),
        type: gameTypeMap[gameType] || 'dice',
        creator,
        wager: ethers.formatEther(wager),
        players,
        state: stateMap[state] || 'pending',
        winner: winner !== ethers.ZeroAddress ? winner : undefined
      };
    } catch (error) {
      logger.error('Failed to get game info', { gameId, error });
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(userAddress: string): Promise<UserStats> {
    try {
      const xpBadges = new ethers.Contract(
        this.contracts.xpBadges,
        this.xpBadgesABI,
        this.provider
      );

      const [totalXP, gamesPlayed, gamesWon, totalDeposited, proposalsVoted, walletsCreated, streakDays] = 
        await xpBadges.getUserStats(userAddress);

      return {
        totalXP: Number(totalXP),
        gamesPlayed: Number(gamesPlayed),
        gamesWon: Number(gamesWon),
        totalDeposited: ethers.formatEther(totalDeposited),
        proposalsVoted: Number(proposalsVoted),
        walletsCreated: Number(walletsCreated),
        streakDays: Number(streakDays)
      };
    } catch (error) {
      logger.error('Failed to get user stats', { userAddress, error });
      throw error;
    }
  }

  /**
   * Get current ETH price (mock implementation)
   */
  async getEthPrice(): Promise<number> {
    // In a real implementation, this would call a price API
    // For now, return a mock price
    return 2000; // $2000 USD
  }

  /**
   * Get wallet balance
   */
  async getWalletBalance(walletAddress: string): Promise<string> {
    try {
      const balance = await this.provider.getBalance(walletAddress);
      return ethers.formatEther(balance);
    } catch (error) {
      logger.error('Failed to get wallet balance', { walletAddress, error });
      throw error;
    }
  }

  /**
   * Get agent wallet address
   */
  getAgentAddress(): string {
    return this.wallet.address;
  }

  /**
   * Get agent wallet balance
   */
  async getAgentBalance(): Promise<string> {
    try {
      const balance = await this.provider.getBalance(this.wallet.address);
      return ethers.formatEther(balance);
    } catch (error) {
      logger.error('Failed to get agent balance', { error });
      throw error;
    }
  }
}
