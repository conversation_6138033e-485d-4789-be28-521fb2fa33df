import React from 'react';
import { Link } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { 
  Wallet, 
  Users, 
  Gamepad2, 
  MessageCircle, 
  Shield, 
  Zap, 
  Trophy,
  ArrowRight,
  Star,
  TrendingUp
} from 'lucide-react';

export const HomePage: React.FC = () => {
  const { isConnected } = useAccount();

  const features = [
    {
      icon: Users,
      title: 'Group Wallets',
      description: 'Create shared wallets with friends for group expenses and collaborative DeFi.',
      color: 'text-blue-400'
    },
    {
      icon: Gamepad2,
      title: 'Mini Games',
      description: 'Play dice and coin flip games with provably fair randomness using Chainlink VRF.',
      color: 'text-green-400'
    },
    {
      icon: MessageCircle,
      title: 'AI Agent',
      description: 'Chat with our AI agent for price alerts, portfolio management, and wallet operations.',
      color: 'text-purple-400'
    },
    {
      icon: Trophy,
      title: 'XP & Badges',
      description: 'Earn XP and collect NFT badges for your activities and achievements.',
      color: 'text-yellow-400'
    },
    {
      icon: Shield,
      title: 'Non-Custodial',
      description: 'Your funds are always under your control with transparent smart contracts.',
      color: 'text-red-400'
    },
    {
      icon: Zap,
      title: 'Base Network',
      description: 'Fast and cheap transactions on Base with seamless user experience.',
      color: 'text-cyan-400'
    }
  ];

  const stats = [
    { label: 'Total Wallets', value: '1,234', icon: Wallet },
    { label: 'Active Users', value: '5,678', icon: Users },
    { label: 'Games Played', value: '12,345', icon: Gamepad2 },
    { label: 'Total Volume', value: '$2.3M', icon: TrendingUp }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight">
            The Future of
            <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">
              {' '}Group Wallets
            </span>
          </h1>
          <p className="text-xl text-secondary-300 max-w-3xl mx-auto leading-relaxed">
            Create shared wallets, play games, and manage funds together with your squad. 
            Powered by XMTP messaging and Base blockchain for the ultimate DeFi experience.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {isConnected ? (
            <>
              <Link
                to="/wallet"
                className="btn-primary flex items-center space-x-2 px-8 py-4 text-lg"
              >
                <Wallet className="w-5 h-5" />
                <span>Open Wallet</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/game"
                className="btn-outline flex items-center space-x-2 px-8 py-4 text-lg"
              >
                <Gamepad2 className="w-5 h-5" />
                <span>Play Games</span>
              </Link>
            </>
          ) : (
            <div className="text-secondary-400">
              Connect your wallet to get started
            </div>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="card text-center">
              <Icon className="w-8 h-8 text-primary-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{stat.value}</div>
              <div className="text-secondary-400 text-sm">{stat.label}</div>
            </div>
          );
        })}
      </section>

      {/* Features Section */}
      <section className="space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white">
            Everything You Need for Group Finance
          </h2>
          <p className="text-lg text-secondary-300 max-w-2xl mx-auto">
            SquadWallet combines the best of DeFi, gaming, and social features 
            in one seamless platform.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="card hover:border-primary-500 transition-colors duration-300">
                <Icon className={`w-12 h-12 ${feature.color} mb-4`} />
                <h3 className="text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-secondary-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </section>

      {/* How It Works Section */}
      <section className="space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white">
            How It Works
          </h2>
          <p className="text-lg text-secondary-300 max-w-2xl mx-auto">
            Get started with SquadWallet in just a few simple steps.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl font-bold text-white">1</span>
            </div>
            <h3 className="text-xl font-semibold text-white">Connect Wallet</h3>
            <p className="text-secondary-300">
              Connect your wallet and join the SquadWallet ecosystem on Base network.
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl font-bold text-white">2</span>
            </div>
            <h3 className="text-xl font-semibold text-white">Create Squad</h3>
            <p className="text-secondary-300">
              Create a shared wallet with your friends or join existing squads.
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl font-bold text-white">3</span>
            </div>
            <h3 className="text-xl font-semibold text-white">Start Playing</h3>
            <p className="text-secondary-300">
              Deposit funds, play games, and manage your squad's finances together.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="card bg-gradient-to-r from-primary-600 to-primary-700 text-center space-y-6">
        <div className="space-y-4">
          <h2 className="text-3xl font-bold text-white">
            Ready to Squad Up?
          </h2>
          <p className="text-primary-100 text-lg max-w-2xl mx-auto">
            Join thousands of users already managing their funds together with SquadWallet.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/wallet"
            className="bg-white text-primary-600 hover:bg-primary-50 font-medium py-3 px-8 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <Star className="w-5 h-5" />
            <span>Get Started</span>
          </Link>
          <Link
            to="/chat"
            className="border border-primary-300 text-white hover:bg-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <MessageCircle className="w-5 h-5" />
            <span>Chat with Agent</span>
          </Link>
        </div>
      </section>
    </div>
  );
};
