import React, { useState, useRef, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { 
  Send, 
  Bot, 
  User, 
  MessageCircle, 
  Zap,
  Copy,
  ExternalLink,
  Wallet,
  Gamepad2,
  TrendingUp
} from 'lucide-react';
import { ChatMessage } from '../types';

export const ChatPage: React.FC = () => {
  const { address, isConnected } = useAccount();
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      content: '👋 Welcome to SquadWallet! I\'m your AI agent assistant. I can help you create wallets, play games, check prices, and more. Type `/help` to see all available commands.',
      sender: 'agent',
      timestamp: new Date(),
      type: 'agent'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickCommands = [
    { command: '/help', description: 'Show all commands', icon: MessageCircle },
    { command: '/create-wallet MySquad', description: 'Create a new wallet', icon: Wallet },
    { command: '/play dice 0.01', description: 'Start a dice game', icon: Gamepad2 },
    { command: '/price ETH', description: 'Get ETH price', icon: TrendingUp },
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: address || 'user',
      timestamp: new Date(),
      type: 'user'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate agent response
    setTimeout(() => {
      const agentResponse = generateAgentResponse(inputMessage);
      const agentMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: agentResponse,
        sender: 'agent',
        timestamp: new Date(),
        type: 'agent'
      };
      setMessages(prev => [...prev, agentMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const generateAgentResponse = (input: string): string => {
    const command = input.toLowerCase().trim();
    
    if (command.startsWith('/help')) {
      return `🤖 **SquadWallet Agent Commands**

**Wallet Management:**
• \`/create-wallet <name>\` - Create a new squad wallet
• \`/deposit <amount>\` - Deposit ETH to your wallet
• \`/balance\` - Check wallet balance
• \`/wallets\` - List your wallets

**Games:**
• \`/play dice <wager>\` - Start a dice game
• \`/play coin <wager>\` - Start a coin flip game
• \`/join <gameId>\` - Join an existing game
• \`/games\` - List active games

**Information:**
• \`/price <token>\` - Get token price
• \`/stats\` - View your statistics
• \`/xp\` - Check your XP and badges
• \`/leaderboard\` - View XP leaderboard

💡 **Tips:**
- Use ETH amounts like "0.1" or "0.1 ETH"
- Games require minimum 0.001 ETH wager
- All transactions are on Base mainnet`;
    }
    
    if (command.startsWith('/create-wallet')) {
      const walletName = input.split(' ').slice(1).join(' ') || 'My Squad';
      return `🎉 **SquadWallet Created Successfully!**

📝 **Name**: ${walletName}
📍 **Address**: \`0x1234...5678\`
🔗 **Transaction**: \`0xabcd...ef12\`
👥 **Members**: 1 (You)

Your new SquadWallet is ready! You can now:
• Deposit funds with \`/deposit <amount>\`
• Invite members to join
• Start playing games with \`/play dice <wager>\`

💡 Share the wallet address with your squad members!`;
    }
    
    if (command.startsWith('/price')) {
      const token = input.split(' ')[1]?.toUpperCase() || 'ETH';
      const price = token === 'ETH' ? 2000 + Math.random() * 100 : 1;
      const change = (Math.random() - 0.5) * 10;
      const emoji = change >= 0 ? '📈' : '📉';
      
      return `💰 **Token Price**

${emoji} **${token}**: $${price.toFixed(2)} (${change >= 0 ? '+' : ''}${change.toFixed(2)}%)

📊 **24h Change**: ${change >= 0 ? '+' : ''}${change.toFixed(2)}%
💵 **Current Price**: $${price.toFixed(2)}

🔄 **Last Updated**: ${new Date().toLocaleTimeString()}`;
    }
    
    if (command.startsWith('/play')) {
      const parts = input.split(' ');
      const gameType = parts[1] || 'dice';
      const wager = parts[2] || '0.01';
      const gameId = Math.floor(Math.random() * 1000) + 100;
      
      return `🎲 **Game Created!**

🎮 **Type**: ${gameType === 'dice' ? 'Dice Roll (highest roll wins)' : 'Coin Flip (50/50 chance)'}
💰 **Wager**: ${wager} ETH
🆔 **Game ID**: ${gameId}
🔗 **Transaction**: \`0x${Math.random().toString(16).substr(2, 8)}...${Math.random().toString(16).substr(2, 4)}\`

🎯 **How to Play**:
• Other players can join with \`/join ${gameId}\`
• Game starts when enough players join
• Winner takes the pot!

💡 **Share the Game ID with friends to let them join!**`;
    }
    
    if (command.startsWith('/stats')) {
      return `📊 **Your SquadWallet Statistics**

👤 **Profile**:
• Address: \`${address?.slice(0, 6)}...${address?.slice(-4)}\`
• Member since: Recently joined

🏆 **Experience**:
• **XP Points**: ${Math.floor(Math.random() * 5000).toLocaleString()}
• **Streak**: ${Math.floor(Math.random() * 30)} days

🏦 **Wallet Activity**:
• **Wallets Created**: ${Math.floor(Math.random() * 5)}
• **Total Deposited**: ${(Math.random() * 10).toFixed(2)} ETH

🎮 **Gaming Stats**:
• **Games Played**: ${Math.floor(Math.random() * 100)}
• **Games Won**: ${Math.floor(Math.random() * 50)}
• **Win Rate**: ${(Math.random() * 100).toFixed(1)}%`;
    }
    
    // Default responses for unrecognized commands
    const responses = [
      "I'm not sure I understand that command. Type `/help` to see all available commands.",
      "That's an interesting request! For now, try using one of the available commands. Type `/help` to see them all.",
      "I'm still learning! Please use one of the supported commands. Type `/help` for the full list.",
      "Command not recognized. Use `/help` to see what I can do for you!"
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleQuickCommand = (command: string) => {
    setInputMessage(command);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (!isConnected) {
    return (
      <div className="text-center py-16">
        <MessageCircle className="w-16 h-16 text-secondary-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">Connect Your Wallet</h2>
        <p className="text-secondary-300">
          Please connect your wallet to chat with the SquadWallet agent.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-white">Chat with Agent</h1>
        <p className="text-secondary-300">
          Interact with the SquadWallet AI agent using natural language or commands
        </p>
      </div>

      {/* Quick Commands */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Commands</h3>
        <div className="grid md:grid-cols-2 gap-3">
          {quickCommands.map((cmd, index) => {
            const Icon = cmd.icon;
            return (
              <button
                key={index}
                onClick={() => handleQuickCommand(cmd.command)}
                className="flex items-center space-x-3 p-3 bg-secondary-700 hover:bg-secondary-600 rounded-lg transition-colors duration-200 text-left"
              >
                <Icon className="w-5 h-5 text-primary-400" />
                <div>
                  <div className="text-white font-medium font-mono text-sm">{cmd.command}</div>
                  <div className="text-secondary-400 text-xs">{cmd.description}</div>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="card">
        {/* Messages */}
        <div className="h-96 overflow-y-auto space-y-4 mb-4 pr-2">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex space-x-3 max-w-3xl ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                {/* Avatar */}
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.type === 'user' ? 'bg-primary-600' : 'bg-secondary-600'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* Message Content */}
                <div className={`rounded-lg p-3 ${
                  message.type === 'user' 
                    ? 'bg-primary-600 text-white' 
                    : 'bg-secondary-700 text-white'
                }`}>
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">
                    {message.content}
                  </div>
                  <div className="flex items-center justify-between mt-2 pt-2 border-t border-opacity-20 border-white">
                    <div className="text-xs opacity-70">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => copyToClipboard(message.content)}
                        className="p-1 hover:bg-white hover:bg-opacity-10 rounded"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex space-x-3 max-w-3xl">
                <div className="w-8 h-8 rounded-full bg-secondary-600 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-secondary-700 rounded-lg p-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="flex space-x-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type a message or command (e.g., /help)..."
            className="input-field flex-1"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isTyping}
            className="btn-primary flex items-center space-x-2 px-4"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-3 text-xs text-secondary-400 text-center">
          💡 Tip: Start your message with "/" for commands, or just chat naturally with the agent
        </div>
      </div>
    </div>
  );
};
