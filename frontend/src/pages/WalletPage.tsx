import React, { useState } from 'react';
import { useAccount } from 'wagmi';
import { 
  Wallet, 
  Plus, 
  Send, 
  Users, 
  TrendingUp, 
  Clock,
  ExternalLink,
  Copy,
  Settings
} from 'lucide-react';

export const WalletPage: React.FC = () => {
  const { address, isConnected } = useAccount();
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'members' | 'proposals'>('overview');

  // Mock data - in real app, this would come from hooks/API
  const wallets = [
    {
      address: '0x1234...5678',
      name: 'Squad Alpha',
      balance: '2.45 ETH',
      members: 4,
      yourShare: '0.61 ETH'
    },
    {
      address: '0x2345...6789',
      name: 'Gaming Squad',
      balance: '1.23 ETH',
      members: 3,
      yourShare: '0.41 ETH'
    }
  ];

  const transactions = [
    {
      id: '1',
      type: 'deposit',
      amount: '0.5 ETH',
      from: '0x1234...5678',
      timestamp: '2 hours ago',
      status: 'completed'
    },
    {
      id: '2',
      type: 'game',
      amount: '0.1 ETH',
      description: 'Dice Game Win',
      timestamp: '5 hours ago',
      status: 'completed'
    },
    {
      id: '3',
      type: 'withdrawal',
      amount: '0.2 ETH',
      to: '0x9876...5432',
      timestamp: '1 day ago',
      status: 'pending'
    }
  ];

  if (!isConnected) {
    return (
      <div className="text-center py-16">
        <Wallet className="w-16 h-16 text-secondary-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">Connect Your Wallet</h2>
        <p className="text-secondary-300">
          Please connect your wallet to view and manage your SquadWallets.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-white">My Wallets</h1>
          <p className="text-secondary-300">Manage your SquadWallets and track your portfolio</p>
        </div>
        <button className="btn-primary flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Create Wallet</span>
        </button>
      </div>

      {/* Wallet Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wallets.map((wallet, index) => (
          <div key={index} className="card hover:border-primary-500 transition-colors duration-300">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{wallet.name}</h3>
              <Settings className="w-5 h-5 text-secondary-400 hover:text-white cursor-pointer" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-300">Total Balance</span>
                <span className="text-white font-medium">{wallet.balance}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-300">Your Share</span>
                <span className="text-primary-400 font-medium">{wallet.yourShare}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-300">Members</span>
                <span className="text-white">{wallet.members}</span>
              </div>
            </div>

            <div className="flex items-center justify-between mt-4 pt-4 border-t border-secondary-700">
              <div className="flex items-center space-x-1 text-secondary-400 text-sm">
                <span>{wallet.address}</span>
                <Copy className="w-3 h-3 cursor-pointer hover:text-white" />
              </div>
              <ExternalLink className="w-4 h-4 text-secondary-400 hover:text-white cursor-pointer" />
            </div>
          </div>
        ))}

        {/* Create New Wallet Card */}
        <div className="card border-dashed border-secondary-600 hover:border-primary-500 transition-colors duration-300 cursor-pointer">
          <div className="text-center py-8">
            <Plus className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Create New Wallet</h3>
            <p className="text-secondary-300 text-sm">
              Start a new SquadWallet with your friends
            </p>
          </div>
        </div>
      </div>

      {/* Detailed View */}
      <div className="card">
        {/* Tabs */}
        <div className="flex space-x-6 border-b border-secondary-700 mb-6">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'transactions', label: 'Transactions', icon: Clock },
            { id: 'members', label: 'Members', icon: Users },
            { id: 'proposals', label: 'Proposals', icon: Send }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 pb-3 border-b-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-400'
                    : 'border-transparent text-secondary-400 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-secondary-700 rounded-lg p-4">
                <div className="text-secondary-300 text-sm mb-1">Total Portfolio</div>
                <div className="text-2xl font-bold text-white">3.68 ETH</div>
                <div className="text-success-400 text-sm">+12.5% (24h)</div>
              </div>
              <div className="bg-secondary-700 rounded-lg p-4">
                <div className="text-secondary-300 text-sm mb-1">Games Played</div>
                <div className="text-2xl font-bold text-white">47</div>
                <div className="text-primary-400 text-sm">Win Rate: 68%</div>
              </div>
              <div className="bg-secondary-700 rounded-lg p-4">
                <div className="text-secondary-300 text-sm mb-1">XP Points</div>
                <div className="text-2xl font-bold text-white">2,450</div>
                <div className="text-warning-400 text-sm">Rank #123</div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Portfolio Chart</h3>
              <div className="bg-secondary-700 rounded-lg h-64 flex items-center justify-center">
                <div className="text-secondary-400">Portfolio chart will be displayed here</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Recent Transactions</h3>
              <button className="text-primary-400 hover:text-primary-300 text-sm">View All</button>
            </div>
            
            <div className="space-y-3">
              {transactions.map((tx) => (
                <div key={tx.id} className="flex items-center justify-between p-4 bg-secondary-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      tx.status === 'completed' ? 'bg-success-400' : 'bg-warning-400'
                    }`} />
                    <div>
                      <div className="text-white font-medium capitalize">{tx.type}</div>
                      <div className="text-secondary-400 text-sm">{tx.timestamp}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">{tx.amount}</div>
                    <div className="text-secondary-400 text-sm capitalize">{tx.status}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Wallet Members</h3>
            <div className="text-secondary-400">Member management interface will be displayed here</div>
          </div>
        )}

        {activeTab === 'proposals' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Active Proposals</h3>
            <div className="text-secondary-400">Proposal voting interface will be displayed here</div>
          </div>
        )}
      </div>
    </div>
  );
};
