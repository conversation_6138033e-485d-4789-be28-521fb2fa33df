import React, { useState } from 'react';
import { useAccount } from 'wagmi';
import { 
  Dice1, 
  Dice2, 
  <PERSON>ce3, 
  <PERSON>ce4, 
  <PERSON>ce5, 
  Dice6,
  Coins,
  Play,
  Users,
  Trophy,
  Clock,
  Zap
} from 'lucide-react';

export const GamePage: React.FC = () => {
  const { isConnected } = useAccount();
  const [activeGame, setActiveGame] = useState<'dice' | 'coin'>('dice');
  const [wagerAmount, setWagerAmount] = useState('0.01');

  // Mock data
  const activeGames = [
    {
      id: 123,
      type: 'dice',
      creator: '0x1234...5678',
      wager: '0.01 ETH',
      players: 2,
      maxPlayers: 6,
      timeLeft: '5m 23s'
    },
    {
      id: 124,
      type: 'coin',
      creator: '0x2345...6789',
      wager: '0.05 ETH',
      players: 1,
      maxPlayers: 2,
      timeLeft: '12m 45s'
    },
    {
      id: 125,
      type: 'dice',
      creator: '0x3456...7890',
      wager: '0.02 ETH',
      players: 4,
      maxPlayers: 6,
      timeLeft: '2m 10s'
    }
  ];

  const gameHistory = [
    {
      id: 120,
      type: 'dice',
      result: 'won',
      amount: '+0.05 ETH',
      players: 3,
      timestamp: '2 hours ago'
    },
    {
      id: 119,
      type: 'coin',
      result: 'lost',
      amount: '-0.02 ETH',
      players: 2,
      timestamp: '5 hours ago'
    },
    {
      id: 118,
      type: 'dice',
      result: 'won',
      amount: '+0.08 ETH',
      players: 5,
      timestamp: '1 day ago'
    }
  ];

  const DiceIcon = ({ number }: { number: number }) => {
    const icons = [Dice1, Dice2, Dice3, Dice4, Dice5, Dice6];
    const Icon = icons[number - 1] || Dice1;
    return <Icon className="w-8 h-8" />;
  };

  if (!isConnected) {
    return (
      <div className="text-center py-16">
        <Dice1 className="w-16 h-16 text-secondary-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">Connect Your Wallet</h2>
        <p className="text-secondary-300">
          Please connect your wallet to start playing games.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-white">Mini Games</h1>
        <p className="text-secondary-300 max-w-2xl mx-auto">
          Play provably fair games with your squad. All games use Chainlink VRF for 
          transparent randomness and automatic payouts.
        </p>
      </div>

      {/* Game Selection */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={() => setActiveGame('dice')}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
            activeGame === 'dice'
              ? 'bg-primary-600 text-white'
              : 'bg-secondary-700 text-secondary-300 hover:text-white hover:bg-secondary-600'
          }`}
        >
          <Dice1 className="w-5 h-5" />
          <span>Dice Roll</span>
        </button>
        <button
          onClick={() => setActiveGame('coin')}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
            activeGame === 'coin'
              ? 'bg-primary-600 text-white'
              : 'bg-secondary-700 text-secondary-300 hover:text-white hover:bg-secondary-600'
          }`}
        >
          <Coins className="w-5 h-5" />
          <span>Coin Flip</span>
        </button>
      </div>

      {/* Game Interface */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Create Game */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-6">
            Create {activeGame === 'dice' ? 'Dice' : 'Coin Flip'} Game
          </h2>

          <div className="space-y-6">
            {/* Game Preview */}
            <div className="bg-secondary-700 rounded-lg p-6 text-center">
              {activeGame === 'dice' ? (
                <div className="space-y-4">
                  <div className="flex justify-center space-x-2">
                    {[1, 2, 3, 4, 5, 6].map((num) => (
                      <DiceIcon key={num} number={num} />
                    ))}
                  </div>
                  <div className="text-white">
                    <div className="font-semibold">Dice Roll Game</div>
                    <div className="text-sm text-secondary-300">
                      Up to 6 players • Highest roll wins
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <Coins className="w-16 h-16 text-yellow-400 mx-auto" />
                  <div className="text-white">
                    <div className="font-semibold">Coin Flip Game</div>
                    <div className="text-sm text-secondary-300">
                      2 players • 50/50 chance
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Wager Input */}
            <div>
              <label className="block text-secondary-300 text-sm font-medium mb-2">
                Wager Amount (ETH)
              </label>
              <input
                type="number"
                value={wagerAmount}
                onChange={(e) => setWagerAmount(e.target.value)}
                min="0.001"
                step="0.001"
                className="input-field w-full"
                placeholder="0.01"
              />
              <div className="text-secondary-400 text-xs mt-1">
                Minimum: 0.001 ETH • Maximum: 10 ETH
              </div>
            </div>

            {/* Create Button */}
            <button className="btn-primary w-full flex items-center justify-center space-x-2">
              <Play className="w-4 h-4" />
              <span>Create Game ({wagerAmount} ETH)</span>
            </button>

            {/* Game Rules */}
            <div className="bg-secondary-700 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">How it works:</h3>
              <ul className="text-secondary-300 text-sm space-y-1">
                {activeGame === 'dice' ? (
                  <>
                    <li>• Up to 6 players can join</li>
                    <li>• Each player gets a random dice roll (1-6)</li>
                    <li>• Highest roll wins the entire pot</li>
                    <li>• 2% house fee deducted from winnings</li>
                  </>
                ) : (
                  <>
                    <li>• Exactly 2 players required</li>
                    <li>• Random coin flip determines winner</li>
                    <li>• Winner takes the entire pot</li>
                    <li>• 2% house fee deducted from winnings</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Active Games */}
        <div className="card">
          <h2 className="text-xl font-semibold text-white mb-6">Active Games</h2>
          
          <div className="space-y-4">
            {activeGames.map((game) => (
              <div key={game.id} className="bg-secondary-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {game.type === 'dice' ? (
                      <Dice1 className="w-5 h-5 text-primary-400" />
                    ) : (
                      <Coins className="w-5 h-5 text-yellow-400" />
                    )}
                    <span className="text-white font-medium">
                      Game #{game.id}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-secondary-400 text-sm">
                    <Clock className="w-4 h-4" />
                    <span>{game.timeLeft}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="text-secondary-400 text-xs">Wager</div>
                    <div className="text-white font-medium">{game.wager}</div>
                  </div>
                  <div>
                    <div className="text-secondary-400 text-xs">Players</div>
                    <div className="text-white font-medium">
                      {game.players}/{game.maxPlayers}
                    </div>
                  </div>
                </div>

                <button className="btn-outline w-full text-sm">
                  Join Game
                </button>
              </div>
            ))}

            {activeGames.length === 0 && (
              <div className="text-center py-8 text-secondary-400">
                No active games. Create one to get started!
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Game History */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Game History</h2>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <Trophy className="w-4 h-4 text-success-400" />
              <span className="text-secondary-300">Win Rate: 68%</span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap className="w-4 h-4 text-primary-400" />
              <span className="text-secondary-300">47 Games Played</span>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {gameHistory.map((game) => (
            <div key={game.id} className="flex items-center justify-between p-4 bg-secondary-700 rounded-lg">
              <div className="flex items-center space-x-3">
                {game.type === 'dice' ? (
                  <Dice1 className="w-5 h-5 text-primary-400" />
                ) : (
                  <Coins className="w-5 h-5 text-yellow-400" />
                )}
                <div>
                  <div className="text-white font-medium">
                    Game #{game.id} • {game.players} players
                  </div>
                  <div className="text-secondary-400 text-sm">{game.timestamp}</div>
                </div>
              </div>
              <div className="text-right">
                <div className={`font-medium ${
                  game.result === 'won' ? 'text-success-400' : 'text-error-400'
                }`}>
                  {game.amount}
                </div>
                <div className="text-secondary-400 text-sm capitalize">
                  {game.result}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
