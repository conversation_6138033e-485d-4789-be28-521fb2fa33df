# XMTP Configuration
XMTP_ENV=production
XMTP_PRIVATE_KEY=your_xmtp_private_key_here

# Coinbase AgentKit Configuration
CDP_API_KEY_NAME=your_cdp_api_key_name
CDP_API_KEY_PRIVATE_KEY=your_cdp_private_key

# Base Network Configuration
BASE_RPC_URL=https://mainnet.base.org
BASE_CHAIN_ID=8453
PRIVATE_KEY=your_deployment_private_key

# Contract Addresses (fill after deployment)
SQUAD_WALLET_FACTORY=
GAME_MANAGER_CONTRACT=
XP_BADGES_CONTRACT=

# External APIs
COINMARKETCAP_API_KEY=your_cmc_api_key
CHAINLINK_VRF_COORDINATOR=******************************************
CHAINLINK_KEY_HASH=0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c

# Frontend Configuration
VITE_WALLET_CONNECT_PROJECT_ID=your_wallet_connect_project_id
VITE_BASE_RPC_URL=https://mainnet.base.org
VITE_SQUAD_WALLET_FACTORY=
VITE_GAME_MANAGER_CONTRACT=
VITE_XP_BADGES_CONTRACT=

# Optional Backend (Railway/Fly)
DATABASE_URL=postgresql://user:password@host:port/database
RAILWAY_STATIC_URL=your_railway_app_url

# Development
NODE_ENV=development
PORT=3000
